import React from 'react';
import { calculateDaysRemaining } from '../utils/dateFormatter';

interface ScholarshipCardProps {
  id: number;
  title: string;
  thumbnail: string;
  deadline: string;
  isOpen: boolean;
  onClick: (id: number) => void;
  level?: string;
  fundingSource?: string;
  country?: string;
}

const ScholarshipCard: React.FC<ScholarshipCardProps> = ({
  id,
  title,
  thumbnail,
  deadline,
  isOpen,
  onClick,
  level,
  country,
}) => {
  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline);

  // Use the calculated isOpen status if available, otherwise use the prop
  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;

  return (
    <div
      className="group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer"
      onClick={() => onClick(id)}
    >
      {/* Thumbnail - Fixed aspect ratio for consistency */}
      <div className="relative w-full aspect-[16/9] overflow-hidden">
        <img
          src={thumbnail ? `http://localhost:5000${thumbnail}` : 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60'}
          alt={title}
          className="w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60';
          }}
        />

        {/* Level badge if available */}
        {level && (
          <div className="absolute top-3 left-3 z-10">
            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${
              level === 'Licence' ? 'text-blue-700' :
              level === 'Master' ? 'text-purple-700' :
              'text-indigo-700'
            }`}>
              {level}
            </span>
          </div>
        )}

        {/* Country overlay */}
        {country && (
          <div className="absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3">
            <span className="text-white text-xs font-medium">{country}</span>
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="p-4 flex-grow flex flex-col">
        {/* Title - 2 lines max with ellipsis */}
        <h3 className="text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200">
          {title}
        </h3>

        {/* Deadline and status */}
        <div className="mt-auto flex items-center justify-between">
          <div className="flex items-center">
            <svg className="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className={`text-sm font-medium ${
              !scholarshipStatus ? 'text-red-600' :
              formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'
            }`}>
              {formattedText}
            </span>
          </div>

          {/* Status badge */}
          <span
            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              scholarshipStatus
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            {scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ScholarshipCard;