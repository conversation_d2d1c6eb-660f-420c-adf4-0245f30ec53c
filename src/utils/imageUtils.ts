/**
 * Production-Grade Image Utilities
 * 
 * This module provides robust image handling utilities for the frontend
 * with proper error handling, fallbacks, and performance optimization.
 */

// Image configuration constants
export const IMAGE_CONFIG = {
  // API base URL
  API_BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  
  // Default fallback images
  DEFAULT_SCHOLARSHIP_IMAGE: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
  DEFAULT_PLACEHOLDER: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTEyLjVMMTg3LjUgMTAwTDIwMCAxMTIuNUwyMTIuNSAxMDBMMjI1IDExMi41VjE0MEgxNzVWMTEyLjVaIiBmaWxsPSIjOUI5QkEwIi8+CjxjaXJjbGUgY3g9IjE4NyIgY3k9IjkwIiByPSI1IiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5QjlCQTAiPkltYWdlPC90ZXh0Pgo8L3N2Zz4K',
  
  // Supported image formats
  SUPPORTED_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],
  
  // Thumbnail sizes
  THUMBNAIL_SIZES: {
    small: { width: 150, height: 150 },
    medium: { width: 300, height: 300 },
    large: { width: 600, height: 400 },
    card: { width: 400, height: 225 }
  },
  
  // Cache settings
  CACHE_DURATION: 3600000, // 1 hour in milliseconds
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // 1 second
};

// Image loading states
export enum ImageLoadState {
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
  FALLBACK = 'fallback'
}

// Image metadata interface
export interface ImageMetadata {
  url: string;
  alt: string;
  width?: number;
  height?: number;
  format?: string;
  size?: 'small' | 'medium' | 'large' | 'card';
}

// Image cache for performance
class ImageCache {
  private cache = new Map<string, { url: string; timestamp: number; state: ImageLoadState }>();

  set(key: string, url: string, state: ImageLoadState): void {
    this.cache.set(key, {
      url,
      timestamp: Date.now(),
      state
    });
  }

  get(key: string): { url: string; state: ImageLoadState } | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > IMAGE_CONFIG.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }

    return { url: cached.url, state: cached.state };
  }

  clear(): void {
    this.cache.clear();
  }
}

const imageCache = new ImageCache();

/**
 * Construct proper image URL with fallback handling
 */
export function constructImageUrl(
  thumbnailPath: string | null | undefined,
  size: 'small' | 'medium' | 'large' | 'card' | 'original' = 'original'
): string {
  // Handle null/undefined/empty paths
  if (!thumbnailPath || thumbnailPath.trim() === '') {
    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;
  }

  // Handle absolute URLs (external images)
  if (thumbnailPath.startsWith('http://') || thumbnailPath.startsWith('https://')) {
    return thumbnailPath;
  }

  // Handle base64 data URLs
  if (thumbnailPath.startsWith('data:')) {
    return thumbnailPath;
  }

  // Construct API URL for uploaded images
  let imageUrl: string;
  
  if (size === 'original') {
    // Original image
    imageUrl = `${IMAGE_CONFIG.API_BASE_URL}${thumbnailPath}`;
  } else {
    // Thumbnail version
    const pathParts = thumbnailPath.split('/');
    const filename = pathParts[pathParts.length - 1];
    const filenameWithoutExt = filename.replace(/\.[^/.]+$/, '');
    const thumbnailFilename = `${filenameWithoutExt}_${size}.webp`;
    
    imageUrl = `${IMAGE_CONFIG.API_BASE_URL}/uploads/scholarships/thumbnails/${thumbnailFilename}`;
  }

  return imageUrl;
}

/**
 * Preload image with retry mechanism
 */
export function preloadImage(url: string, retryCount = 0): Promise<ImageLoadState> {
  return new Promise((resolve) => {
    // Check cache first
    const cached = imageCache.get(url);
    if (cached) {
      resolve(cached.state);
      return;
    }

    const img = new Image();
    
    img.onload = () => {
      imageCache.set(url, url, ImageLoadState.LOADED);
      resolve(ImageLoadState.LOADED);
    };

    img.onerror = () => {
      if (retryCount < IMAGE_CONFIG.MAX_RETRY_ATTEMPTS) {
        // Retry after delay
        setTimeout(() => {
          preloadImage(url, retryCount + 1).then(resolve);
        }, IMAGE_CONFIG.RETRY_DELAY * (retryCount + 1));
      } else {
        imageCache.set(url, url, ImageLoadState.ERROR);
        resolve(ImageLoadState.ERROR);
      }
    };

    img.src = url;
  });
}

/**
 * Get optimized image URL with fallback chain
 */
export async function getOptimizedImageUrl(
  thumbnailPath: string | null | undefined,
  size: 'small' | 'medium' | 'large' | 'card' = 'card'
): Promise<string> {
  // Try thumbnail first
  const thumbnailUrl = constructImageUrl(thumbnailPath, size);
  const thumbnailState = await preloadImage(thumbnailUrl);
  
  if (thumbnailState === ImageLoadState.LOADED) {
    return thumbnailUrl;
  }

  // Fallback to original image
  const originalUrl = constructImageUrl(thumbnailPath, 'original');
  const originalState = await preloadImage(originalUrl);
  
  if (originalState === ImageLoadState.LOADED) {
    return originalUrl;
  }

  // Final fallback to default image
  return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;
}

/**
 * Generate srcSet for responsive images
 */
export function generateSrcSet(thumbnailPath: string | null | undefined): string {
  if (!thumbnailPath) {
    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;
  }

  const sizes = ['small', 'medium', 'large'] as const;
  const srcSetEntries = sizes.map(size => {
    const url = constructImageUrl(thumbnailPath, size);
    const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];
    return `${url} ${dimensions.width}w`;
  });

  return srcSetEntries.join(', ');
}

/**
 * Get image metadata for SEO and accessibility
 */
export function getImageMetadata(
  thumbnailPath: string | null | undefined,
  title: string,
  size: 'small' | 'medium' | 'large' | 'card' = 'card'
): ImageMetadata {
  const url = constructImageUrl(thumbnailPath, size);
  const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];
  
  return {
    url,
    alt: `${title} - Scholarship thumbnail`,
    width: dimensions.width,
    height: dimensions.height,
    format: 'webp',
    size
  };
}

/**
 * Validate image URL format
 */
export function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  
  // Check for valid URL patterns
  const urlPattern = /^(https?:\/\/|data:|\/)/;
  if (!urlPattern.test(url)) return false;
  
  // Check for supported formats (if it's a file URL)
  if (url.includes('.')) {
    const extension = url.split('.').pop()?.toLowerCase();
    return extension ? IMAGE_CONFIG.SUPPORTED_FORMATS.includes(extension) : false;
  }
  
  return true;
}

/**
 * Handle image loading errors with graceful fallback
 */
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackUrl?: string
): void {
  const img = event.target as HTMLImageElement;
  
  // Prevent infinite loop
  if (img.src === IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE) {
    return;
  }
  
  // Try custom fallback first
  if (fallbackUrl && img.src !== fallbackUrl) {
    img.src = fallbackUrl;
    return;
  }
  
  // Use default fallback
  img.src = IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;
}

/**
 * Clear image cache (useful for memory management)
 */
export function clearImageCache(): void {
  imageCache.clear();
}

/**
 * Get image loading placeholder
 */
export function getImagePlaceholder(): string {
  return IMAGE_CONFIG.DEFAULT_PLACEHOLDER;
}
