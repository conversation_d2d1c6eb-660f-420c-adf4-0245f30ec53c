[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx": "159"}, {"size": 274, "mtime": 1745947759865, "results": "160", "hashOfConfig": "161"}, {"size": 2118, "mtime": 1745983514069, "results": "162", "hashOfConfig": "161"}, {"size": 5438, "mtime": 1745977740794, "results": "163", "hashOfConfig": "161"}, {"size": 10242, "mtime": 1745981563339, "results": "164", "hashOfConfig": "161"}, {"size": 1742, "mtime": 1745977713025, "results": "165", "hashOfConfig": "161"}, {"size": 7131, "mtime": 1745981562258, "results": "166", "hashOfConfig": "161"}, {"size": 10498, "mtime": 1745982258490, "results": "167", "hashOfConfig": "161"}, {"size": 3076, "mtime": 1745945973317, "results": "168", "hashOfConfig": "161"}, {"size": 553, "mtime": 1745978322072, "results": "169", "hashOfConfig": "161"}, {"size": 6250, "mtime": 1745977773426, "results": "170", "hashOfConfig": "161"}, {"size": 2446, "mtime": 1745945132621, "results": "171", "hashOfConfig": "161"}, {"size": 2645, "mtime": 1745977581979, "results": "172", "hashOfConfig": "161"}, {"size": 4167, "mtime": 1745981843303, "results": "173", "hashOfConfig": "161"}, {"size": 5713, "mtime": 1745981758114, "results": "174", "hashOfConfig": "161"}, {"size": 675, "mtime": 1745976791748, "results": "175", "hashOfConfig": "161"}, {"size": 1059, "mtime": 1745976720607, "results": "176", "hashOfConfig": "161"}, {"size": 3452, "mtime": 1745946003719, "results": "177", "hashOfConfig": "161"}, {"size": 2518, "mtime": 1745983866923, "results": "178", "hashOfConfig": "161"}, {"size": 1737, "mtime": 1745978376608, "results": "179", "hashOfConfig": "161"}, {"size": 4075, "mtime": 1745982269507, "results": "180", "hashOfConfig": "161"}, {"size": 4531, "mtime": 1745982263875, "results": "181", "hashOfConfig": "161"}, {"size": 5406, "mtime": 1745982274929, "results": "182", "hashOfConfig": "161"}, {"size": 2535, "mtime": 1745978386143, "results": "183", "hashOfConfig": "161"}, {"size": 737, "mtime": 1745944438688, "results": "184", "hashOfConfig": "161"}, {"size": 2323, "mtime": 1745982233889, "results": "185", "hashOfConfig": "161"}, {"size": 274, "mtime": 1745947759865, "results": "186", "hashOfConfig": "187"}, {"size": 4324, "mtime": 1746276088446, "results": "188", "hashOfConfig": "187"}, {"size": 7131, "mtime": 1745981562258, "results": "189", "hashOfConfig": "187"}, {"size": 10498, "mtime": 1745982258490, "results": "190", "hashOfConfig": "187"}, {"size": 10242, "mtime": 1745981563339, "results": "191", "hashOfConfig": "187"}, {"size": 1742, "mtime": 1745977713025, "results": "192", "hashOfConfig": "187"}, {"size": 8315, "mtime": 1746204095547, "results": "193", "hashOfConfig": "187"}, {"size": 3076, "mtime": 1745945973317, "results": "194", "hashOfConfig": "187"}, {"size": 2535, "mtime": 1746033992389, "results": "195", "hashOfConfig": "187"}, {"size": 1737, "mtime": 1745978376608, "results": "196", "hashOfConfig": "187"}, {"size": 6250, "mtime": 1745977773426, "results": "197", "hashOfConfig": "187"}, {"size": 553, "mtime": 1745978322072, "results": "198", "hashOfConfig": "187"}, {"size": 2446, "mtime": 1745945132621, "results": "199", "hashOfConfig": "187"}, {"size": 5154, "mtime": 1746276100698, "results": "200", "hashOfConfig": "187"}, {"size": 8097, "mtime": 1745983506688, "results": "201", "hashOfConfig": "187"}, {"size": 6024, "mtime": 1746026061709, "results": "202", "hashOfConfig": "187"}, {"size": 9559, "mtime": 1746270350218, "results": "203", "hashOfConfig": "187"}, {"size": 737, "mtime": 1745944438688, "results": "204", "hashOfConfig": "187"}, {"size": 2645, "mtime": 1745977581979, "results": "205", "hashOfConfig": "187"}, {"size": 4531, "mtime": 1745982263875, "results": "206", "hashOfConfig": "187"}, {"size": 4075, "mtime": 1745982269507, "results": "207", "hashOfConfig": "187"}, {"size": 5406, "mtime": 1745982274929, "results": "208", "hashOfConfig": "187"}, {"size": 5713, "mtime": 1745981758114, "results": "209", "hashOfConfig": "187"}, {"size": 675, "mtime": 1745976791748, "results": "210", "hashOfConfig": "187"}, {"size": 4167, "mtime": 1745981843303, "results": "211", "hashOfConfig": "187"}, {"size": 2115, "mtime": 1746029576846, "results": "212", "hashOfConfig": "187"}, {"size": 1689, "mtime": 1745982730905, "results": "213", "hashOfConfig": "187"}, {"size": 19288, "mtime": 1745983400384, "results": "214", "hashOfConfig": "187"}, {"size": 1059, "mtime": 1745976720607, "results": "215", "hashOfConfig": "187"}, {"size": 2535, "mtime": 1745978386143, "results": "216", "hashOfConfig": "187"}, {"size": 14052, "mtime": 1746274142489, "results": "217", "hashOfConfig": "187"}, {"size": 3939, "mtime": 1746017528736, "results": "218", "hashOfConfig": "187"}, {"size": 13857, "mtime": 1746282401482, "results": "219", "hashOfConfig": "187"}, {"size": 11940, "mtime": 1746252382064, "results": "220", "hashOfConfig": "187"}, {"size": 38877, "mtime": 1746252407519, "results": "221", "hashOfConfig": "187"}, {"size": 8393, "mtime": 1746249939564, "results": "222", "hashOfConfig": "187"}, {"size": 11544, "mtime": 1746272209267, "results": "223", "hashOfConfig": "187"}, {"size": 1343, "mtime": 1746033436995, "results": "224", "hashOfConfig": "187"}, {"size": 1752, "mtime": 1746274101102, "results": "225", "hashOfConfig": "187"}, {"size": 3341, "mtime": 1746199132190, "results": "226", "hashOfConfig": "187"}, {"size": 7045, "mtime": 1746199160974, "results": "227", "hashOfConfig": "187"}, {"size": 4103, "mtime": 1746200520123, "results": "228", "hashOfConfig": "187"}, {"size": 6276, "mtime": 1746249196201, "results": "229", "hashOfConfig": "187"}, {"size": 7222, "mtime": 1746249216418, "results": "230", "hashOfConfig": "187"}, {"size": 1105, "mtime": 1746201832350, "results": "231", "hashOfConfig": "187"}, {"size": 921, "mtime": 1746202207790, "results": "232", "hashOfConfig": "187"}, {"size": 11379, "mtime": 1746276323665, "results": "233", "hashOfConfig": "187"}, {"size": 1059, "mtime": 1746226321253, "results": "234", "hashOfConfig": "187"}, {"size": 7914, "mtime": 1746251582912, "results": "235", "hashOfConfig": "187"}, {"size": 4784, "mtime": 1746252717773, "results": "236", "hashOfConfig": "187"}, {"size": 1777, "mtime": 1746254015165, "results": "237", "hashOfConfig": "187"}, {"size": 9435, "mtime": 1746252638103, "results": "238", "hashOfConfig": "187"}, {"size": 5504, "mtime": 1746275135511, "results": "239", "hashOfConfig": "187"}, {"size": 620, "mtime": 1747275499301, "results": "240", "hashOfConfig": "241"}, {"size": 5105, "mtime": 1752347512363, "results": "242", "hashOfConfig": "241"}, {"size": 13828, "mtime": 1752248890943, "results": "243", "hashOfConfig": "241"}, {"size": 19195, "mtime": 1747280789154, "results": "244", "hashOfConfig": "241"}, {"size": 3587, "mtime": 1747235972243, "results": "245", "hashOfConfig": "241"}, {"size": 10653, "mtime": 1747235897205, "results": "246", "hashOfConfig": "241"}, {"size": 3076, "mtime": 1745945973317, "results": "247", "hashOfConfig": "241"}, {"size": 1974, "mtime": 1747278879917, "results": "248", "hashOfConfig": "241"}, {"size": 553, "mtime": 1745978322072, "results": "249", "hashOfConfig": "241"}, {"size": 18635, "mtime": 1747235596822, "results": "250", "hashOfConfig": "251"}, {"size": 2446, "mtime": 1745945132621, "results": "252", "hashOfConfig": "241"}, {"size": 19874, "mtime": 1752251800113, "results": "253", "hashOfConfig": "241"}, {"size": 5624, "mtime": 1752347411216, "results": "254", "hashOfConfig": "241"}, {"size": 11326, "mtime": 1752283692783, "results": "255", "hashOfConfig": "241"}, {"size": 14078, "mtime": 1752283590200, "results": "256", "hashOfConfig": "241"}, {"size": 12834, "mtime": 1752281264858, "results": "257", "hashOfConfig": "241"}, {"size": 7045, "mtime": 1746199160974, "results": "258", "hashOfConfig": "241"}, {"size": 3341, "mtime": 1746199132190, "results": "259", "hashOfConfig": "241"}, {"size": 1059, "mtime": 1746226321253, "results": "260", "hashOfConfig": "241"}, {"size": 6276, "mtime": 1746249196201, "results": "261", "hashOfConfig": "241"}, {"size": 921, "mtime": 1746202207790, "results": "262", "hashOfConfig": "241"}, {"size": 1695, "mtime": 1747186871230, "results": "263", "hashOfConfig": "251"}, {"size": 3161, "mtime": 1747232764014, "results": "264", "hashOfConfig": "251"}, {"size": 6917, "mtime": 1752345873971, "results": "265", "hashOfConfig": "241"}, {"size": 12973, "mtime": 1747224498475, "results": "266", "hashOfConfig": "241"}, {"size": 6346, "mtime": 1752345903201, "results": "267", "hashOfConfig": "241"}, {"size": 8228, "mtime": 1752345931858, "results": "268", "hashOfConfig": "241"}, {"size": 8023, "mtime": 1752346082272, "results": "269", "hashOfConfig": "241"}, {"size": 675, "mtime": 1745976791748, "results": "270", "hashOfConfig": "251"}, {"size": 7860, "mtime": 1752251788545, "results": "271", "hashOfConfig": "241"}, {"size": 7222, "mtime": 1746249216418, "results": "272", "hashOfConfig": "241"}, {"size": 9917, "mtime": 1752283723085, "results": "273", "hashOfConfig": "241"}, {"size": 3917, "mtime": 1752349784089, "results": "274", "hashOfConfig": "241"}, {"size": 2535, "mtime": 1745978386143, "results": "275", "hashOfConfig": "241"}, {"size": 3877, "mtime": 1747235933700, "results": "276", "hashOfConfig": "241"}, {"size": 1689, "mtime": 1745982730905, "results": "277", "hashOfConfig": "241"}, {"size": 3697, "mtime": 1747184461868, "results": "278", "hashOfConfig": "251"}, {"size": 959, "mtime": 1747186815101, "results": "279", "hashOfConfig": "251"}, {"size": 5906, "mtime": 1752248869494, "results": "280", "hashOfConfig": "241"}, {"size": 2847, "mtime": 1747187027857, "results": "281", "hashOfConfig": "241"}, {"size": 2604, "mtime": 1747279467729, "results": "282", "hashOfConfig": "241"}, {"size": 8119, "mtime": 1747220020952, "results": "283", "hashOfConfig": "251"}, {"size": 8243, "mtime": 1747220059414, "results": "284", "hashOfConfig": "251"}, {"size": 3071, "mtime": 1747221577347, "results": "285", "hashOfConfig": "251"}, {"size": 6125, "mtime": 1747221750779, "results": "286", "hashOfConfig": "251"}, {"size": 6017, "mtime": 1747221715802, "results": "287", "hashOfConfig": "251"}, {"size": 3890, "mtime": 1747221780672, "results": "288", "hashOfConfig": "251"}, {"size": 3377, "mtime": 1747221613654, "results": "289", "hashOfConfig": "251"}, {"size": 3156, "mtime": 1747221640258, "results": "290", "hashOfConfig": "251"}, {"size": 7752, "mtime": 1747237735157, "results": "291", "hashOfConfig": "241"}, {"size": 11148, "mtime": 1747241866444, "results": "292", "hashOfConfig": "241"}, {"size": 9820, "mtime": 1747279005156, "results": "293", "hashOfConfig": "241"}, {"size": 8196, "mtime": 1747279261336, "results": "294", "hashOfConfig": "241"}, {"size": 9024, "mtime": 1747241959417, "results": "295", "hashOfConfig": "241"}, {"size": 9620, "mtime": 1747242054549, "results": "296", "hashOfConfig": "241"}, {"size": 9567, "mtime": 1747242099457, "results": "297", "hashOfConfig": "241"}, {"size": 5262, "mtime": 1747242002891, "results": "298", "hashOfConfig": "241"}, {"size": 5483, "mtime": 1752349839659, "results": "299", "hashOfConfig": "241"}, {"size": 28904, "mtime": 1752248907873, "results": "300", "hashOfConfig": "241"}, {"size": 1536, "mtime": 1747237627552, "results": "301", "hashOfConfig": "241"}, {"size": 2783, "mtime": 1747275467037, "results": "302", "hashOfConfig": "241"}, {"size": 4730, "mtime": 1747275582856, "results": "303", "hashOfConfig": "241"}, {"size": 5097, "mtime": 1747302359006, "results": "304", "hashOfConfig": "251"}, {"size": 1008, "mtime": 1752283918060, "results": "305", "hashOfConfig": "241"}, {"size": 9653, "mtime": 1752283637791, "results": "306", "hashOfConfig": "241"}, {"size": 358, "mtime": 1752254238558, "results": "307", "hashOfConfig": "241"}, {"size": 3337, "mtime": 1752254433306, "results": "308", "hashOfConfig": "241"}, {"size": 1433, "mtime": 1752283753270, "results": "309", "hashOfConfig": "241"}, {"size": 3435, "mtime": 1752283114606, "results": "310", "hashOfConfig": "241"}, {"size": 4212, "mtime": 1752287116909, "results": "311", "hashOfConfig": "241"}, {"size": 1083, "mtime": 1752283244823, "results": "312", "hashOfConfig": "241"}, {"size": 5685, "mtime": 1752287194809, "results": "313", "hashOfConfig": "241"}, {"size": 16723, "mtime": 1752348343896, "results": "314", "hashOfConfig": "241"}, {"size": 14378, "mtime": 1752304560424, "results": "315", "hashOfConfig": "241"}, {"size": 16323, "mtime": 1747156326467, "results": "316", "hashOfConfig": "241"}, {"size": 8275, "mtime": 1752346642724, "results": "317", "hashOfConfig": "241"}, {"size": 11030, "mtime": 1752346737219, "results": "318", "hashOfConfig": "241"}, {"size": 15352, "mtime": 1752346794610, "results": "319", "hashOfConfig": "241"}, {"size": 11560, "mtime": 1752348377288, "results": "320", "hashOfConfig": "241"}, {"size": 10639, "mtime": 1752347902146, "results": "321", "hashOfConfig": "241"}, {"size": 14557, "mtime": 1752347930663, "results": "322", "hashOfConfig": "241"}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["800"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["801", "802", "803"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["804"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["805"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["806", "807"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["808", "809", "810"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["811"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["812", "813"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["814", "815", "816"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["817"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["818"], ["819"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["820"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["821"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["822"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["823"], ["824"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["825"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["826", "827"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["828", "829", "830"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["831", "832"], ["833"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["834", "835"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["836", "837", "838", "839"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["840"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["841"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["842"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["843"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["844"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["845"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", ["846"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["847", "848"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["849", "850"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["851"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["852"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["853"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["854", "855", "856"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx", ["857"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx", ["858"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx", ["859"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx", ["860"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx", ["861"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx", [], [], {"ruleId": "862", "severity": 1, "message": "863", "line": 170, "column": 7, "nodeType": "864", "messageId": "865", "endLine": 170, "endColumn": 15}, {"ruleId": "866", "severity": 1, "message": "867", "line": 51, "column": 15, "nodeType": "868", "endLine": 51, "endColumn": 74}, {"ruleId": "866", "severity": 1, "message": "867", "line": 57, "column": 15, "nodeType": "868", "endLine": 57, "endColumn": 74}, {"ruleId": "866", "severity": 1, "message": "867", "line": 63, "column": 15, "nodeType": "868", "endLine": 63, "endColumn": 74}, {"ruleId": "862", "severity": 1, "message": "863", "line": 170, "column": 7, "nodeType": "864", "messageId": "865", "endLine": 170, "endColumn": 15}, {"ruleId": "869", "severity": 1, "message": "870", "line": 44, "column": 6, "nodeType": "871", "endLine": 44, "endColumn": 68, "suggestions": "872"}, {"ruleId": "862", "severity": 1, "message": "873", "line": 3, "column": 8, "nodeType": "864", "messageId": "865", "endLine": 3, "endColumn": 19}, {"ruleId": "862", "severity": 1, "message": "874", "line": 31, "column": 11, "nodeType": "864", "messageId": "865", "endLine": 31, "endColumn": 23}, {"ruleId": "866", "severity": 1, "message": "867", "line": 51, "column": 15, "nodeType": "868", "endLine": 51, "endColumn": 74}, {"ruleId": "866", "severity": 1, "message": "867", "line": 57, "column": 15, "nodeType": "868", "endLine": 57, "endColumn": 74}, {"ruleId": "866", "severity": 1, "message": "867", "line": 63, "column": 15, "nodeType": "868", "endLine": 63, "endColumn": 74}, {"ruleId": "862", "severity": 1, "message": "874", "line": 34, "column": 11, "nodeType": "864", "messageId": "865", "endLine": 34, "endColumn": 23}, {"ruleId": "862", "severity": 1, "message": "875", "line": 10, "column": 3, "nodeType": "864", "messageId": "865", "endLine": 10, "endColumn": 8}, {"ruleId": "862", "severity": 1, "message": "876", "line": 15, "column": 24, "nodeType": "864", "messageId": "865", "endLine": 15, "endColumn": 43}, {"ruleId": "862", "severity": 1, "message": "877", "line": 3, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 3, "endColumn": 21}, {"ruleId": "862", "severity": 1, "message": "878", "line": 44, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 44, "endColumn": 17}, {"ruleId": "879", "severity": 1, "message": "880", "line": 162, "column": 36, "nodeType": "881", "messageId": "882", "endLine": 162, "endColumn": 60}, {"ruleId": "869", "severity": 1, "message": "883", "line": 37, "column": 6, "nodeType": "871", "endLine": 37, "endColumn": 42, "suggestions": "884", "suppressions": "885"}, {"ruleId": "862", "severity": 1, "message": "886", "line": 236, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 236, "endColumn": 30}, {"ruleId": "869", "severity": 1, "message": "883", "line": 64, "column": 6, "nodeType": "871", "endLine": 64, "endColumn": 61, "suggestions": "887", "suppressions": "888"}, {"ruleId": "869", "severity": 1, "message": "883", "line": 29, "column": 6, "nodeType": "871", "endLine": 29, "endColumn": 31, "suggestions": "889", "suppressions": "890"}, {"ruleId": "869", "severity": 1, "message": "891", "line": 25, "column": 6, "nodeType": "871", "endLine": 25, "endColumn": 8, "suggestions": "892", "suppressions": "893"}, {"ruleId": "869", "severity": 1, "message": "894", "line": 177, "column": 6, "nodeType": "871", "endLine": 177, "endColumn": 8, "suggestions": "895", "suppressions": "896"}, {"ruleId": "862", "severity": 1, "message": "877", "line": 3, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 3, "endColumn": 21}, {"ruleId": "869", "severity": 1, "message": "897", "line": 37, "column": 6, "nodeType": "871", "endLine": 37, "endColumn": 8, "suggestions": "898", "suppressions": "899"}, {"ruleId": "869", "severity": 1, "message": "870", "line": 44, "column": 6, "nodeType": "871", "endLine": 44, "endColumn": 68, "suggestions": "900"}, {"ruleId": "866", "severity": 1, "message": "867", "line": 135, "column": 21, "nodeType": "868", "endLine": 135, "endColumn": 96}, {"ruleId": "866", "severity": 1, "message": "867", "line": 141, "column": 21, "nodeType": "868", "endLine": 141, "endColumn": 96}, {"ruleId": "866", "severity": 1, "message": "867", "line": 108, "column": 21, "nodeType": "868", "endLine": 108, "endColumn": 94}, {"ruleId": "866", "severity": 1, "message": "867", "line": 114, "column": 21, "nodeType": "868", "endLine": 114, "endColumn": 94}, {"ruleId": "866", "severity": 1, "message": "867", "line": 120, "column": 21, "nodeType": "868", "endLine": 120, "endColumn": 94}, {"ruleId": "862", "severity": 1, "message": "901", "line": 2, "column": 105, "nodeType": "864", "messageId": "865", "endLine": 2, "endColumn": 111}, {"ruleId": "862", "severity": 1, "message": "902", "line": 3, "column": 111, "nodeType": "864", "messageId": "865", "endLine": 3, "endColumn": 126}, {"ruleId": "869", "severity": 1, "message": "883", "line": 45, "column": 6, "nodeType": "871", "endLine": 45, "endColumn": 31, "suggestions": "903", "suppressions": "904"}, {"ruleId": "862", "severity": 1, "message": "878", "line": 44, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 44, "endColumn": 17}, {"ruleId": "869", "severity": 1, "message": "905", "line": 106, "column": 6, "nodeType": "871", "endLine": 106, "endColumn": 8, "suggestions": "906"}, {"ruleId": "866", "severity": 1, "message": "867", "line": 117, "column": 17, "nodeType": "868", "endLine": 117, "endColumn": 131}, {"ruleId": "866", "severity": 1, "message": "867", "line": 125, "column": 17, "nodeType": "868", "endLine": 125, "endColumn": 131}, {"ruleId": "866", "severity": 1, "message": "867", "line": 133, "column": 17, "nodeType": "868", "endLine": 133, "endColumn": 131}, {"ruleId": "866", "severity": 1, "message": "867", "line": 141, "column": 17, "nodeType": "868", "endLine": 141, "endColumn": 131}, {"ruleId": "869", "severity": 1, "message": "897", "line": 37, "column": 6, "nodeType": "871", "endLine": 37, "endColumn": 8, "suggestions": "907", "suppressions": "908"}, {"ruleId": "869", "severity": 1, "message": "891", "line": 25, "column": 6, "nodeType": "871", "endLine": 25, "endColumn": 8, "suggestions": "909", "suppressions": "910"}, {"ruleId": "862", "severity": 1, "message": "911", "line": 38, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 38, "endColumn": 12}, {"ruleId": "866", "severity": 1, "message": "867", "line": 101, "column": 26, "nodeType": "868", "endLine": 101, "endColumn": 87}, {"ruleId": "862", "severity": 1, "message": "912", "line": 2, "column": 40, "nodeType": "864", "messageId": "865", "endLine": 2, "endColumn": 44}, {"ruleId": "869", "severity": 1, "message": "913", "line": 38, "column": 6, "nodeType": "871", "endLine": 38, "endColumn": 8, "suggestions": "914"}, {"ruleId": "862", "severity": 1, "message": "915", "line": 25, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 25, "endColumn": 21}, {"ruleId": "866", "severity": 1, "message": "867", "line": 186, "column": 21, "nodeType": "868", "endLine": 186, "endColumn": 74}, {"ruleId": "866", "severity": 1, "message": "867", "line": 188, "column": 21, "nodeType": "868", "endLine": 188, "endColumn": 74}, {"ruleId": "862", "severity": 1, "message": "912", "line": 4, "column": 30, "nodeType": "864", "messageId": "865", "endLine": 4, "endColumn": 34}, {"ruleId": "862", "severity": 1, "message": "916", "line": 7, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 7, "endColumn": 16}, {"ruleId": "917", "severity": 1, "message": "918", "line": 172, "column": 1, "nodeType": "919", "endLine": 179, "endColumn": 3}, {"ruleId": "862", "severity": 1, "message": "916", "line": 2, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 2, "endColumn": 16}, {"ruleId": "862", "severity": 1, "message": "920", "line": 5, "column": 16, "nodeType": "864", "messageId": "865", "endLine": 5, "endColumn": 20}, {"ruleId": "862", "severity": 1, "message": "921", "line": 13, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 13, "endColumn": 20}, {"ruleId": "862", "severity": 1, "message": "922", "line": 15, "column": 10, "nodeType": "864", "messageId": "865", "endLine": 15, "endColumn": 21}, {"ruleId": "869", "severity": 1, "message": "923", "line": 106, "column": 6, "nodeType": "871", "endLine": 106, "endColumn": 8, "suggestions": "924"}, {"ruleId": "862", "severity": 1, "message": "925", "line": 10, "column": 3, "nodeType": "864", "messageId": "865", "endLine": 10, "endColumn": 12}, {"ruleId": "862", "severity": 1, "message": "874", "line": 32, "column": 11, "nodeType": "864", "messageId": "865", "endLine": 32, "endColumn": 23}, {"ruleId": "869", "severity": 1, "message": "926", "line": 26, "column": 6, "nodeType": "871", "endLine": 26, "endColumn": 24, "suggestions": "927"}, {"ruleId": "869", "severity": 1, "message": "928", "line": 35, "column": 6, "nodeType": "871", "endLine": 35, "endColumn": 15, "suggestions": "929"}, {"ruleId": "869", "severity": 1, "message": "930", "line": 47, "column": 6, "nodeType": "871", "endLine": 47, "endColumn": 44, "suggestions": "931"}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["932"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["933"], ["934"], "'sendEmailNotification' is assigned a value but never used.", ["935"], ["936"], ["937"], ["938"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["939"], ["940"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["941"], ["942"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["943"], ["944"], ["945"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["946"], ["947"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["948"], ["949"], ["950"], ["951"], ["952"], "'api' is assigned a value but never used.", "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["953"], "'scrollToTabs' is assigned a value but never used.", "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Text' is assigned a value but never used.", "'DateFormat' is defined but never used.", "'ApiResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["954"], "'XMarkIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGuides'. Either include it or remove the dependency array.", ["955"], "React Hook useEffect has a missing dependency: 'fetchOpportunities'. Either include it or remove the dependency array.", ["956"], "React Hook useEffect has missing dependencies: 'fetchScholarships' and 'fetchStatistics'. Either include them or remove the dependency array.", ["957"], {"desc": "958", "fix": "959"}, {"desc": "960", "fix": "961"}, {"kind": "962", "justification": "963"}, {"desc": "964", "fix": "965"}, {"kind": "962", "justification": "963"}, {"desc": "966", "fix": "967"}, {"kind": "962", "justification": "963"}, {"desc": "968", "fix": "969"}, {"kind": "962", "justification": "963"}, {"desc": "970", "fix": "971"}, {"kind": "962", "justification": "963"}, {"desc": "972", "fix": "973"}, {"kind": "962", "justification": "963"}, {"desc": "958", "fix": "974"}, {"desc": "966", "fix": "975"}, {"kind": "962", "justification": "963"}, {"desc": "976", "fix": "977"}, {"desc": "972", "fix": "978"}, {"kind": "962", "justification": "963"}, {"desc": "968", "fix": "979"}, {"kind": "962", "justification": "963"}, {"desc": "980", "fix": "981"}, {"desc": "982", "fix": "983"}, {"desc": "984", "fix": "985"}, {"desc": "986", "fix": "987"}, {"desc": "988", "fix": "989"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "990", "text": "991"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "992", "text": "993"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "994", "text": "995"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "996", "text": "997"}, "Update the dependencies array to be: [initializeSetup]", {"range": "998", "text": "999"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1002", "text": "1003"}, {"range": "1004", "text": "991"}, {"range": "1005", "text": "997"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1006", "text": "1007"}, {"range": "1008", "text": "1003"}, {"range": "1009", "text": "999"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1010", "text": "1011"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1012", "text": "1013"}, "Update the dependencies array to be: [fetchGuides, selectedCategory]", {"range": "1014", "text": "1015"}, "Update the dependencies array to be: [fetchOpportunities, filters]", {"range": "1016", "text": "1017"}, "Update the dependencies array to be: [decodedCountry, currentPage, filters, fetchScholarships, fetchStatistics]", {"range": "1018", "text": "1019"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1914, 1939], [3435, 3437], "[adminInfo, stats]", [1201, 1203], [969, 971], [1326, 1328], "[backgrounds.length]", [3118, 3120], "[fetchSecurityEvents]", [734, 752], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]", [934, 943], "[fetchOpportunities, filters]", [1294, 1332], "[decodedCountry, currentPage, filters, fetchScholarships, fetchStatistics]"]